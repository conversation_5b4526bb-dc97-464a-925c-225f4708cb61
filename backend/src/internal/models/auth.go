package models

import (
	"crypto/rand"
	"crypto/sha256"
	"database/sql/driver"
	"encoding/hex"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
)

// User represents the user model (Django auth_user equivalent)
type User struct {
	ID          uint       `gorm:"primaryKey;autoIncrement" json:"id"`
	Username    string     `gorm:"unique;not null;size:150" json:"username"`
	Email       string     `gorm:"size:254" json:"email"`
	FirstName   string     `gorm:"size:150" json:"first_name"`
	LastName    string     `gorm:"size:150" json:"last_name"`
	IsActive    bool       `gorm:"default:true" json:"is_active"`
	IsStaff     bool       `gorm:"default:false" json:"is_staff"`
	IsSuperuser bool       `gorm:"default:false" json:"is_superuser"`
	DateJoined  time.Time  `gorm:"autoCreateTime" json:"date_joined"`
	LastLogin   *time.Time `json:"last_login"`
	Password    string     `gorm:"size:128" json:"-"` // Hidden from JSON
}

// TableName returns the table name for the User model
func (User) TableName() string {
	return "auth_user"
}

// EndpointList represents a list of allowed endpoints
type EndpointList []string

// Value implements the driver.Valuer interface for database storage
func (e EndpointList) Value() (driver.Value, error) {
	if len(e) == 0 {
		return nil, nil
	}
	return strings.Join(e, ","), nil
}

// Scan implements the sql.Scanner interface for database retrieval
func (e *EndpointList) Scan(value any) error {
	if value == nil {
		*e = nil
		return nil
	}

	str, ok := value.(string)
	if !ok {
		return fmt.Errorf("cannot scan %T into EndpointList", value)
	}

	if str == "" {
		*e = nil
		return nil
	}

	endpoints := strings.Split(str, ",")
	for i, endpoint := range endpoints {
		endpoints[i] = strings.TrimSpace(endpoint)
	}
	*e = endpoints
	return nil
}

// APIKey represents the API key model (Django authentication_apikey equivalent)
type APIKey struct {
	ID               uint         `gorm:"primaryKey;autoIncrement" json:"id"`
	OwnerID          uint         `gorm:"not null" json:"owner_id"`
	Owner            User         `gorm:"foreignKey:OwnerID;constraint:OnDelete:CASCADE" json:"owner"`
	Name             string       `gorm:"size:255;not null" json:"name"`
	Key              string       `gorm:"size:64;unique;not null" json:"key"`
	HashedKey        string       `gorm:"size:64;unique;not null" json:"-"`
	CreatedAt        time.Time    `gorm:"autoCreateTime" json:"created_at"`
	Revoked          bool         `gorm:"default:false" json:"revoked"`
	ExpiresAt        *time.Time   `json:"expires_at"`
	Permission       string       `gorm:"size:20;default:'read_write'" json:"permission"`
	AllowedEndpoints EndpointList `gorm:"type:text" json:"allowed_endpoints"`
	Used             bool         `gorm:"default:false" json:"used"`
}

// TableName returns the table name for the APIKey model
func (APIKey) TableName() string {
	return "authentication_apikey"
}

// BeforeCreate generates API key and hashed key before creating the record
func (a *APIKey) BeforeCreate(tx *gorm.DB) error {
	if a.Key == "" {
		a.Key = generateAPIKey()
		a.HashedKey = hashKey(a.Key)
	}
	return nil
}

// generateAPIKey generates a 64-character hexadecimal API key
func generateAPIKey() string {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		// Fallback if crypto/rand fails: generate bytes from a time-based xorshift
		seed := uint64(time.Now().UnixNano())
		for i := range bytes {
			// xorshift-like scrambling
			seed ^= seed << 13
			seed ^= seed >> 7
			seed ^= seed << 17
			seed ^= uint64(i*31 + 17)
			bytes[i] = byte(seed)
		}
	}
	return hex.EncodeToString(bytes)
}

// hashKey creates a SHA256 hash of the API key
func hashKey(key string) string {
	hash := sha256.Sum256([]byte(key))
	return hex.EncodeToString(hash[:])
}

// IsExpired checks if the API key has expired
func (a *APIKey) IsExpired() bool {
	if a.ExpiresAt == nil {
		return false
	}
	return time.Now().After(*a.ExpiresAt)
}

// IsEndpointAllowed checks if a specific endpoint is allowed for this API key
func (a *APIKey) IsEndpointAllowed(endpoint string) bool {
	if len(a.AllowedEndpoints) == 0 {
		return true // If no restrictions, allow all endpoints
	}

	// Normalize the endpoint path
	normalizedEndpoint := strings.TrimPrefix(endpoint, "/")

	for _, allowed := range a.AllowedEndpoints {
		// Support both with and without '/api' prefix
		candidates := []string{
			strings.TrimPrefix(allowed, "/"),
			strings.TrimPrefix("api/"+allowed, "/"),
		}

		for _, candidate := range candidates {
			// Check exact match or subtree (prefix boundary)
			if normalizedEndpoint == candidate ||
				strings.HasPrefix(normalizedEndpoint, candidate+"/") {
				return true
			}
		}
	}

	return false
}

// AuthResult represents the result of authentication verification
type AuthResult struct {
	Valid            bool         `json:"valid"`
	UserID           *uint        `json:"user_id,omitempty"`
	Username         *string      `json:"username,omitempty"`
	Permission       string       `json:"permission"`
	AllowedEndpoints EndpointList `json:"allowed_endpoints,omitempty"`
	Error            string       `json:"error,omitempty"`
}

// LoginRequest represents the login request structure
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// LoginResponse represents the login response structure
type LoginResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
}

// RefreshRequest represents the refresh token request structure
type RefreshRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// VerifyRequest represents the token verification request structure
type VerifyRequest struct {
	Token string `json:"token" binding:"required"`
}

// APIKeyCreateRequest represents the API key creation request structure
type APIKeyCreateRequest struct {
	Name             string   `json:"name" binding:"required"`
	Permission       string   `json:"permission"`
	ExpiresInDays    *int     `json:"expires_in_days"`
	AllowedEndpoints []string `json:"allowed_endpoints"`
}

// APIKeyResponse represents the API key response structure
type APIKeyResponse struct {
	ID               uint         `json:"id"`
	Name             string       `json:"name"`
	Key              string       `json:"key,omitempty"` // Only included when creating
	Permission       string       `json:"permission"`
	AllowedEndpoints EndpointList `json:"allowed_endpoints"`
	CreatedAt        time.Time    `json:"created_at"`
	ExpiresAt        *time.Time   `json:"expires_at"`
	Revoked          bool         `json:"revoked"`
	Used             bool         `json:"used"`
}
