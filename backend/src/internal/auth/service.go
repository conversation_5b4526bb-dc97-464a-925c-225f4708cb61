// Package auth provides authentication and authorization services
package auth

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"log"
	"slices"
	"time"

	"gnrs-go/internal/cache"
	"gnrs-go/internal/config"
	"gnrs-go/internal/db"
	"gnrs-go/internal/models"

	"gorm.io/gorm"
)

// Service handles authentication operations
type Service struct {
	db         *db.DB
	cache      *cache.Client
	jwtManager *JWTManager
	cfg        *config.Config
}

// NewService creates a new authentication service
func NewService(database *db.DB, cacheClient *cache.Client, cfg *config.Config) *Service {
	return &Service{
		db:         database,
		cache:      cacheClient,
		jwtManager: NewJWTManager(cfg),
		cfg:        cfg,
	}
}

// Login authenticates a user and returns JWT tokens
func (s *Service) Login(ctx context.Context, req *models.LoginRequest) (*models.LoginResponse, error) {
	// Find user by username
	var user models.User
	if err := s.db.Where("username = ? AND is_active = ?", req.Username, true).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("invalid credentials")
		}
		return nil, fmt.Errorf("database error: %w", err)
	}

	// TODO: Implement proper password verification
	// For now, we'll skip password verification as the original Django auth is complex
	// In production, this should verify against Django's password hash format

	// Update last login
	now := time.Now()
	user.LastLogin = &now
	if err := s.db.Save(&user).Error; err != nil {
		log.Printf("warning: failed to update last login for user %d: %v", user.ID, err)
	}

	// Generate tokens
	tokens, err := s.jwtManager.GenerateTokens(&user)
	if err != nil {
		return nil, fmt.Errorf("failed to generate tokens: %w", err)
	}

	// Cache the user for faster subsequent verifications
	cacheKey := fmt.Sprintf("user:%d", user.ID)
	if err := s.cache.Set(ctx, cacheKey, user, 5*time.Minute); err != nil {
		// Non-fatal: proceed even if cache write fails
		log.Printf("warning: cache set failed for %s: %v", cacheKey, err)
	}

	return tokens, nil
}

// VerifyToken verifies a JWT token and returns user information
func (s *Service) VerifyToken(ctx context.Context, tokenString string) (*models.AuthResult, error) {
	// Verify the token
	claims, err := s.jwtManager.VerifyToken(tokenString)
	if err != nil {
		return &models.AuthResult{
			Valid: false,
			Error: err.Error(),
		}, nil
	}

	// Check cache first
	cacheKey := fmt.Sprintf("user:%d", claims.UserID)
	var user models.User
	if err := s.cache.Get(ctx, cacheKey, &user); err != nil {
		// Cache miss, fetch from database
		if err := s.db.Where("id = ? AND is_active = ?", claims.UserID, true).First(&user).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				return &models.AuthResult{
					Valid: false,
					Error: "user not found",
				}, nil
			}
			return &models.AuthResult{
				Valid: false,
				Error: "database error",
			}, nil
		}

		// Cache the user
		if err := s.cache.Set(ctx, cacheKey, user, 5*time.Minute); err != nil {
			log.Printf("warning: cache set failed for %s: %v", cacheKey, err)
		}
	}

	return &models.AuthResult{
		Valid:      true,
		UserID:     &user.ID,
		Username:   &user.Username,
		Permission: "read_write", // Bearer tokens get full permissions
	}, nil
}

// VerifyAPIKey verifies an API key and returns authentication information
func (s *Service) VerifyAPIKey(ctx context.Context, apiKey string) (*models.AuthResult, error) {
	// Hash the provided key
	hashedKey := hashAPIKey(apiKey)

	// Check cache first
	cacheKey := fmt.Sprintf("apikey:%s", hashedKey)
	var cachedResult models.AuthResult
	if err := s.cache.Get(ctx, cacheKey, &cachedResult); err == nil {
		return &cachedResult, nil
	}

	// Find API key in database
	var key models.APIKey
	if err := s.db.Preload("Owner").Where("hashed_key = ? AND revoked = ?", hashedKey, false).First(&key).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			result := &models.AuthResult{
				Valid: false,
				Error: "invalid API key",
			}
			// Cache negative result for a short time
			if err := s.cache.Set(ctx, cacheKey, result, 1*time.Minute); err != nil {
				log.Printf("warning: cache set failed for %s: %v", cacheKey, err)
			}
			return result, nil
		}
		return &models.AuthResult{
			Valid: false,
			Error: "database error",
		}, nil
	}

	// Check if key is expired
	if key.IsExpired() {
		result := &models.AuthResult{
			Valid: false,
			Error: "API key has expired",
		}
		if err := s.cache.Set(ctx, cacheKey, result, 1*time.Minute); err != nil {
			log.Printf("warning: cache set failed for %s: %v", cacheKey, err)
		}
		return result, nil
	}

	// Check if owner is active
	if !key.Owner.IsActive {
		result := &models.AuthResult{
			Valid: false,
			Error: "API key owner is inactive",
		}
		if err := s.cache.Set(ctx, cacheKey, result, 1*time.Minute); err != nil {
			log.Printf("warning: cache set failed for %s: %v", cacheKey, err)
		}
		return result, nil
	}

	// Mark key as used if not already
	if !key.Used {
		key.Used = true
		if err := s.db.Save(&key).Error; err != nil {
			log.Printf("warning: failed to mark API key used (id=%d): %v", key.ID, err)
		}
	}

	result := &models.AuthResult{
		Valid:            true,
		UserID:           &key.Owner.ID,
		Username:         &key.Owner.Username,
		Permission:       key.Permission,
		AllowedEndpoints: key.AllowedEndpoints,
	}

	// Cache the result
	if err := s.cache.Set(ctx, cacheKey, result, 5*time.Minute); err != nil {
		log.Printf("warning: cache set failed for %s: %v", cacheKey, err)
	}

	return result, nil
}

// RefreshToken generates a new access token from a refresh token
func (s *Service) RefreshToken(ctx context.Context, req *models.RefreshRequest) (*models.LoginResponse, error) {
	// Verify the refresh token
	claims, err := s.jwtManager.VerifyToken(req.RefreshToken)
	if err != nil {
		return nil, fmt.Errorf("invalid refresh token: %w", err)
	}

	if claims.TokenType != "refresh" {
		return nil, fmt.Errorf("token is not a refresh token")
	}

	// Get user from database
	var user models.User
	if err := s.db.Where("id = ? AND is_active = ?", claims.UserID, true).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("database error: %w", err)
	}

	// Generate new tokens
	return s.jwtManager.RefreshToken(req.RefreshToken, &user)
}

// CreateAPIKey creates a new API key for a user
func (s *Service) CreateAPIKey(ctx context.Context, userID uint, req *models.APIKeyCreateRequest) (*models.APIKeyResponse, error) {
	// Validate permission
	validPermissions := []string{"read_only", "write_only", "read_write"}
	permission := req.Permission
	if permission == "" {
		permission = "read_write"
	}

	if !slices.Contains(validPermissions, permission) {
		return nil, fmt.Errorf("invalid permission: %s", permission)
	}

	// Create API key
	apiKey := &models.APIKey{
		OwnerID:          userID,
		Name:             req.Name,
		Permission:       permission,
		AllowedEndpoints: req.AllowedEndpoints,
	}

	// Set expiration if provided
	if req.ExpiresInDays != nil && *req.ExpiresInDays > 0 {
		expiresAt := time.Now().AddDate(0, 0, *req.ExpiresInDays)
		apiKey.ExpiresAt = &expiresAt
	}

	// Save to database
	if err := s.db.Create(apiKey).Error; err != nil {
		return nil, fmt.Errorf("failed to create API key: %w", err)
	}

	return &models.APIKeyResponse{
		ID:               apiKey.ID,
		Name:             apiKey.Name,
		Key:              apiKey.Key, // Include key only when creating
		Permission:       apiKey.Permission,
		AllowedEndpoints: apiKey.AllowedEndpoints,
		CreatedAt:        apiKey.CreatedAt,
		ExpiresAt:        apiKey.ExpiresAt,
		Revoked:          apiKey.Revoked,
		Used:             apiKey.Used,
	}, nil
}

// hashAPIKey creates a SHA256 hash of an API key
func hashAPIKey(key string) string {
	hash := sha256.Sum256([]byte(key))
	return hex.EncodeToString(hash[:])
}
