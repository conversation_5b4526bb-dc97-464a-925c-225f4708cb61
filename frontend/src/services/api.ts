import axios from "axios";

// Base URL can be overridden via env; defaults to the provided backend host
const API_BASE = (import.meta as any)?.env?.VITE_API_BASE || "/api";

export const api = axios.create({
  baseURL: API_BASE,
  withCredentials: false,
});

// Attach Authorization header from session storage to avoid tight coupling with Pinia in this module
api.interceptors.request.use((config) => {
  const token = typeof window !== "undefined" ? sessionStorage.getItem("auth_token") : null;
  if (token) {
    config.headers = config.headers || {};
    (config.headers as any)["Authorization"] = `Bearer ${token}`;
  }
  return config;
});

api.interceptors.response.use(
  (resp) => resp,
  (error) => {
    if (error?.response?.status === 401) {
      // Clear token on unauthorized
      if (typeof window !== "undefined") {
        sessionStorage.removeItem("auth_token");
      }
    }
    return Promise.reject(error);
  }
);

export interface LoginResponse {
  token: string;
}

export interface ApiKey {
  id: string;
  key: string;
  createdAt?: string;
  revoked?: boolean;
}

export const login = async (username: string, password: string) => {
  return api.post<LoginResponse>("/admin/login", { username, password });
};

export const getApiKeys = async () => {
  return api.get<ApiKey[]>("/admin/api-keys");
};

export const generateApiKey = async () => {
  return api.post<ApiKey>("/admin/api-keys");
};

export const revokeApiKey = async (id: string) => {
  return api.delete<void>(`/admin/api-keys/${id}`);
};

