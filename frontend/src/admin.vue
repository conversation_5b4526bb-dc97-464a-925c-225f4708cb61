<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { login, getApi<PERSON><PERSON><PERSON>, generate<PERSON><PERSON><PERSON><PERSON>, revoke<PERSON><PERSON><PERSON><PERSON>, type <PERSON><PERSON><PERSON><PERSON> } from "./services/api";
import { useAuthStore } from "./stores/auth";

const auth = useAuthStore();

// Login state
const username = ref("");
const password = ref("");
const loggingIn = ref(false);
const loginError = ref<string | null>(null);

// API keys state
const keys = ref<ApiKey[]>([]);
const loadingKeys = ref(false);
const generating = ref(false);
const newKey = ref<string | null>(null);
const actionError = ref<string | null>(null);

const isAuthed = computed(() => auth.isAuthenticated);

const doLogin = async () => {
  loginError.value = null;
  actionError.value = null;
  newKey.value = null;
  loggingIn.value = true;
  try {
    const { data } = await login(username.value, password.value);
    if (data?.token) {
      auth.setToken(data.token);
      await fetchKeys();
    } else {
      loginError.value = "Login berhasil tetapi token tidak diterima.";
    }
  } catch (e: any) {
    loginError.value = e?.response?.data?.message || "Login gagal. Periksa kredensial Anda.";
  } finally {
    loggingIn.value = false;
  }
};

const fetchKeys = async () => {
  loadingKeys.value = true;
  actionError.value = null;
  try {
    const { data } = await getApiKeys();
    keys.value = data || [];
  } catch (e: any) {
    actionError.value = e?.response?.data?.message || "Gagal memuat API keys.";
  } finally {
    loadingKeys.value = false;
  }
};

const doGenerate = async () => {
  generating.value = true;
  actionError.value = null;
  try {
    const { data } = await generateApiKey();
    if (data?.key) newKey.value = data.key;
    // Refresh list
    await fetchKeys();
  } catch (e: any) {
    actionError.value = e?.response?.data?.message || "Gagal membuat API key baru.";
  } finally {
    generating.value = false;
  }
};

const doRevoke = async (id: string) => {
  actionError.value = null;
  try {
    await revokeApiKey(id);
    keys.value = keys.value.filter((k) => k.id !== id);
  } catch (e: any) {
    actionError.value = e?.response?.data?.message || "Gagal mencabut API key.";
  }
};

const logout = () => {
  auth.clear();
  keys.value = [];
  username.value = "";
  password.value = "";
  newKey.value = null;
};

onMounted(async () => {
  if (isAuthed.value) {
    await fetchKeys();
  }
});
</script>

<template>
  <div class="form-container" style="max-width: 680px;">
    <h1 class="form-title">Admin</h1>

    <!-- Not authenticated: Login form -->
    <div v-if="!isAuthed" style="text-align:left;">
      <div class="form-group">
        <label for="username">Username</label>
        <input id="username" v-model.trim="username" type="text" placeholder="Masukkan username" />
      </div>
      <div class="form-group">
        <label for="password">Password</label>
        <input id="password" v-model="password" type="password" placeholder="Masukkan password" />
      </div>
      <button :disabled="loggingIn" @click="doLogin">
        {{ loggingIn ? "Masuk..." : "Masuk" }}
      </button>
      <div v-if="loginError" class="error-message">{{ loginError }}</div>
    </div>

    <!-- Authenticated: API Key Management -->
    <div v-else>
      <div style="display:flex; justify-content: space-between; align-items:center;">
        <div class="form-subtitle">Manajemen API Keys</div>
        <button class="secondary" @click="logout">Keluar</button>
      </div>

      <div class="spaci"></div>

      <div>
        <button :disabled="generating" @click="doGenerate">
          {{ generating ? "Membuat..." : "Buat API Key Baru" }}
        </button>
        <div v-if="newKey" style="margin-top:10px;">
          <strong>API Key baru:</strong>
          <div style="word-break:break-all;">{{ newKey }}</div>
          <small>Simpan key ini sekarang. Demi keamanan, key mungkin tidak akan ditampilkan lagi.</small>
        </div>
      </div>

      <div class="spaci"></div>

      <div>
        <div class="form-subtitle" style="margin-bottom:10px;">Daftar API Keys</div>
        <div v-if="loadingKeys">Memuat daftar...</div>
        <div v-else>
          <div v-if="keys.length === 0" style="color: var(--color-muted);">Belum ada API key.</div>
          <ul v-else style="list-style:none; padding:0;">
            <li v-for="k in keys" :key="k.id" style="display:flex; align-items:center; justify-content:space-between; padding:10px; border-bottom:1px solid rgba(46,90,53,0.1);">
              <div style="flex:1; min-width:0;">
                <div style="font-weight:600; word-break:break-all;">{{ k.key }}</div>
                <div style="font-size:12px; color: var(--color-muted);">
                  ID: {{ k.id }}
                  <span v-if="k.createdAt"> • {{ new Date(k.createdAt).toLocaleString() }}</span>
                  <span v-if="k.revoked"> • Dicabut</span>
                </div>
              </div>
              <button class="secondary" style="margin-left:10px;" @click="doRevoke(k.id)">Cabut</button>
            </li>
          </ul>
        </div>
      </div>

      <div v-if="actionError" class="error-message">{{ actionError }}</div>
    </div>
  </div>
</template>

<style scoped>
/**** Uses global tokens and button style: #2e5a35, radius 20px, padding ****/
</style>

