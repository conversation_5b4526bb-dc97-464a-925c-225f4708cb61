import {
  createRouter,
  createWebH<PERSON>ory,
  RouteRecordRaw,
  RouteComponent,
} from "vue-router";

// Define the type for our component map
interface ComponentMap {
  [key: string]: () => Promise<RouteComponent>;
}

// Lazy-loaded route components for better performance
const componentMap: ComponentMap = {
  BiodataGenerus: () => import("./biodata-generus.vue"),
  PantauBiodataGenerus: () => import("./pantau-biodata-generus.vue"),
};

// Define the type for our route configuration
interface RouteConfig {
  path: string;
  name: string;
  title: string;
}

const routesConfig: RouteConfig[] = [
  {
    path: "/biodata-generus",
    name: "BiodataGenerus",
    title: "Biodata Generus",
  },
  {
    path: "/pantau-biodata-generus",
    name: "PantauBiodataGenerus",
    title: "Pantauan Biodata Generus",
  },
];

const routes: RouteRecordRaw[] = routesConfig.map(
  (route: RouteConfig): RouteRecordRaw => {
    // Destructure and extract needed properties, ignoring title since it's used via route.title
    const { name, path } = route;
    return {
      name,
      path,
      component: componentMap[name],
      meta: {
        title: route.title,
        requiresAuth: false,
      },
    } as RouteRecordRaw;
  },
);

const router = createRouter({
  history: createWebHistory(),
  routes,
});

export default router;
